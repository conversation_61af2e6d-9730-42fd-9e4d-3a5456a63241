import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/app.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/initialize.dart';
import 'package:xr_helper/xr_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  try {
    await initialize();

    Log.i('App initialization completed');
  } catch (e) {
    Log.e('Initialization Error: $e');
  }

  runApp(
    const ProviderScope(
      child: BaseApp(),
    ),
  );
}
